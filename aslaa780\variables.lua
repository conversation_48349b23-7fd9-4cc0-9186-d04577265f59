-- variables.lua - Simplified shared variables for Air780EG
local variables = {}

-- Basic states
variables.relay1_state = 0
variables.relay2_state = 0
variables.key_state = false
variables.sound_flag = true
variables.gps_flag = true
variables.carAlreadyStarted = false
variables.isLicensed = true

-- Communication
variables.sms_data = nil
variables.callback_number = nil
variables.phone_number1 = "88392933"  -- Your phone number (last 8 digits)
variables.phone_number2 = nil
variables.phone_number3 = nil

-- Timing
variables.currentTime = 0
variables.timers_queue = {}

-- Settings
variables.voltage_offset = 0
variables.voltage_threshold = 0.5
variables.voltage_notify_flag = false
variables.geely_atlas_mode = false

-- Timing parameters (ms)
variables.lock_init_duration = 2000
variables.lock_press_duration = 1000
variables.unlock_press_duration = 1000
variables.lock_wait_duration = 2000
variables.unlock_wait_duration = 1000
variables.relay1_on_duration = 3000

return variables
